<template>
  <div class="p-4 rounded-lg bg-white border border-surface-300">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-800">Edit Item</h2>
      <Button
        icon="pi pi-times"
        size="small"
        text
        severity="secondary"
        @click="cancelEdit"
        v-tooltip="'Cancel'"
      />
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div class="p-field">
        <label for="itemType" class="block text-sm font-medium text-gray-700 mb-1">Item Type</label>
        <Select
          id="itemType"
          v-model="editItem.type"
          :options="itemTypes"
          placeholder="Select Item Type"
          class="w-full"
        />
      </div>
      <div class="p-field">
        <label for="itemTitle" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
        <InputText
          id="itemTitle"
          v-model="editItem.title"
          placeholder="Enter item title"
          class="w-full"
        />
      </div>
    </div>

    <!-- Question Type -->
    <div v-if="editItem.type === 'question'" class="space-y-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
        <InputText v-model="editItem.question" placeholder="Enter your question" class="w-full" />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-2">Answer Options</label>
        <div class="space-y-2">
          <div v-for="(option, index) in editItem.options" :key="index" class="flex items-center gap-3 p-2 border border-gray-200 rounded">
            <RadioButton :value="index" v-model="editItem.correctAnswer" />
            <InputText v-model="option.text" placeholder="Enter option text" class="flex-1" />
            <span class="text-xs text-gray-500">{{ index === editItem.correctAnswer ? 'Correct' : '' }}</span>
            <Button
              icon="pi pi-trash"
              size="small"
              text
              severity="danger"
              @click="removeOption(index)"
              v-tooltip="'Remove option'"
            />
          </div>
        </div>
        <Button
          label="Add Option"
          icon="pi pi-plus"
          size="small"
          outlined
          @click="addOption"
          class="mt-2"
        />
      </div>
    </div>

    <!-- Text Type -->
    <div v-else-if="editItem.type === 'text'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Text Content</label>
      <Textarea
        v-model="editItem.content"
        placeholder="Enter your text content"
        rows="4"
        class="w-full resize-none"
      />
    </div>

    <!-- Image Type -->
    <div v-else-if="editItem.type === 'image'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Link Type -->
    <div v-else-if="editItem.type === 'link'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">URL</label>
      <InputText
        v-model="editItem.url"
        placeholder="https://example.com"
        class="w-full"
      />
    </div>

    <!-- Map Type -->
    <div v-else-if="editItem.type === 'map'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Map Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Map image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Diagram Type -->
    <div v-else-if="editItem.type === 'diagram'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Diagram Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Diagram image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Timed Question Type -->
    <div v-else-if="editItem.type === 'timed-question'" class="space-y-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
        <InputText v-model="editItem.question" placeholder="Enter your open-ended question" class="w-full" />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Predefined Answer</label>
        <Textarea
          v-model="editItem.timedAnswer"
          placeholder="Enter the answer that will be revealed after the timer"
          rows="3"
          class="w-full resize-none"
        />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Reveal Time (seconds)</label>
        <InputText
          v-model="editItem.revealTimeSeconds"
          type="number"
          placeholder="30"
          min="5"
          max="300"
          class="w-full"
        />
        <small class="text-gray-500">Time before the answer is revealed (5-300 seconds)</small>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-3 mt-6 pt-4 border-t border-gray-200">
      <Button
        label="Save Changes"
        icon="pi pi-check"
        @click="saveChanges"
        :disabled="!editItem.type || !editItem.title"
        class="flex-1"
      />
      <Button
        label="Cancel"
        icon="pi pi-times"
        severity="secondary"
        outlined
        @click="cancelEdit"
        class="flex-1"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';

interface Props {
  item: Item;
  bookId: string;
  chapterId: string;
  onSave?: (updatedItem: Item) => void;
  onCancel?: () => void;
}

const props = defineProps<Props>();
const store = useMainStore();
const route = useRoute();

const editItem = ref<Item>({ ...props.item });
const itemTypes = ['question', 'text', 'image', 'link', 'map', 'diagram', 'timed-question'];

onMounted(() => {
  // Ensure options array exists for questions
  if (editItem.value.type === 'question' && !editItem.value.options) {
    editItem.value.options = [];
  }
});

const addOption = () => {
  if (!editItem.value.options) {
    editItem.value.options = [];
  }
  editItem.value.options.push({ text: '' });
};

const removeOption = (index: number) => {
  if (editItem.value.options && editItem.value.options.length > 1) {
    editItem.value.options.splice(index, 1);
    // Adjust correct answer if needed
    if (editItem.value.correctAnswer !== undefined && editItem.value.correctAnswer >= index) {
      if (editItem.value.correctAnswer === index) {
        editItem.value.correctAnswer = undefined;
      } else {
        editItem.value.correctAnswer--;
      }
    }
  }
};

const saveChanges = async () => {
  try {
    const updatedItem = await store.updateItem(
      props.bookId,
      props.chapterId,
      editItem.value.id,
      editItem.value
    );
    
    if (updatedItem && props.onSave) {
      props.onSave(updatedItem);
    }
  } catch (error) {
    console.error('Error updating item:', error);
  }
};

const cancelEdit = () => {
  if (props.onCancel) {
    props.onCancel();
  }
};
</script>

<style scoped>
/* Custom styles if needed */
</style>
