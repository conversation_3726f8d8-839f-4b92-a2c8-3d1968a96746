<template>
  <div class="p-4 rounded-lg bg-white border border-surface-300">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div class="p-field">
        <label for="itemType" class="block text-sm font-medium text-gray-700 mb-1">Item Type</label>
        <Select
          id="itemType"
          v-model="item.type"
          :options="itemTypes"
          placeholder="Select Item Type"
          class="w-full"
        />
      </div>
      <div class="p-field">
        <label for="itemTitle" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
        <InputText
          id="itemTitle"
          v-model="item.title"
          placeholder="Enter item title"
          class="w-full"
        />
      </div>
    </div>

    <!-- Question Type -->
    <div v-if="item.type === 'question'" class="space-y-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
        <InputText v-model="item.question" placeholder="Enter your question" class="w-full" />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-2">Answer Options</label>
        <div class="space-y-2">
          <div v-for="(option, index) in item.options" :key="index" class="flex items-center gap-3 p-2 border border-gray-200 rounded">
            <RadioButton :value="index" v-model="item.correctAnswer" />
            <InputText v-model="option.text" placeholder="Enter option text" class="flex-1" />
            <span class="text-xs text-gray-500">{{ index === item.correctAnswer ? 'Correct' : '' }}</span>
          </div>
        </div>
        <Button
          label="Add Option"
          icon="pi pi-plus"
          size="small"
          outlined
          @click="addOption"
          class="mt-2"
        />
      </div>
    </div>

    <!-- Text Type -->
    <div v-else-if="item.type === 'text'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Text Content</label>
      <Textarea
        v-model="item.content"
        placeholder="Enter your text content"
        rows="4"
        class="w-full resize-none"
      />
    </div>

    <!-- Image Type -->
    <div v-else-if="item.type === 'image'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Link Type -->
    <div v-else-if="item.type === 'link'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">URL</label>
      <InputText
        v-model="item.url"
        placeholder="https://example.com"
        class="w-full"
      />
    </div>

    <!-- Map Type -->
    <div v-else-if="item.type === 'map'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Map Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Map image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Diagram Type -->
    <div v-else-if="item.type === 'diagram'" class="p-field">
      <label class="block text-sm font-medium text-gray-700 mb-1">Diagram Image Upload</label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
        <small class="text-gray-500">Diagram image upload preview only (no backend)</small>
      </div>
    </div>

    <!-- Timed Question Type -->
    <div v-else-if="item.type === 'timed-question'" class="space-y-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
        <InputText v-model="item.question" placeholder="Enter your open-ended question" class="w-full" />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Predefined Answer</label>
        <Textarea
          v-model="item.timedAnswer"
          placeholder="Enter the answer that will be revealed after the timer"
          rows="3"
          class="w-full resize-none"
        />
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Reveal Time (seconds)</label>
        <InputText
          v-model="item.revealTimeSeconds"
          type="number"
          placeholder="30"
          min="5"
          max="300"
          class="w-full"
        />
        <small class="text-gray-500">Time before the answer is revealed (5-300 seconds)</small>
      </div>
    </div>

    <!-- Save Button -->
    <div class="flex justify-end pt-4 border-t border-gray-200 mt-6">
      <Button
        label="Save Item"
        icon="pi pi-save"
        @click="saveItem"
        :disabled="!item.type || !item.title"
        class="px-6"
      />
    </div>

    <!-- QR Code Display for newly created items -->
    <div v-if="lastCreatedItem" class="mt-6 p-4 border rounded-lg bg-green-50 border-green-200">
      <div class="flex items-center mb-4">
        <i class="pi pi-check-circle text-green-600 mr-2"></i>
        <h3 class="text-lg font-semibold text-green-800">Item Created Successfully!</h3>
      </div>
      <QRCodeDisplay
        :bookId="route.params.id as string"
        :chapterId="props.chapterId!"
        :itemId="lastCreatedItem.id"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select  from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import QRCodeDisplay from './QRCodeDisplay.vue';

interface Props {
  chapterId?: string;
  onItemCreated?: () => void;
}

const props = defineProps<Props>();
const store = useMainStore();
const route = useRoute();
const item = ref<Partial<Item>>({
  type: undefined,
  title: '',
  options: [],
  correctAnswer: undefined,
  timedAnswer: '',
  revealTimeSeconds: 30
});
const lastCreatedItem = ref<Item | null>(null);
const itemTypes = ['question', 'text', 'image', 'link', 'map', 'diagram', 'timed-question'];

watch(() => props.chapterId, () => {
  item.value = {
    type: undefined,
    title: '',
    options: [],
    correctAnswer: undefined,
    timedAnswer: '',
    revealTimeSeconds: 30
  };
  lastCreatedItem.value = null; // Clear QR code when switching chapters
});

const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    const { id, ...itemData } = { ...item.value } as Item;
    const newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    lastCreatedItem.value = newItem;
    item.value = {
      type: undefined,
      title: '',
      options: [],
      correctAnswer: undefined,
      timedAnswer: '',
      revealTimeSeconds: 30
    };

    // Call the callback to refresh the tree data
    if (props.onItemCreated) {
      props.onItemCreated();
    }
  }
};
</script>