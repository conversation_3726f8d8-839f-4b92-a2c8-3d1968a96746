<template>
  <div class="qr-code-container mx-auto">
    <div v-if="loading" class="text-center p-4">
      <i class="pi pi-spin pi-spinner text-2xl"></i>
      <p class="mt-2">Generating QR Code...</p>
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-500">
      <i class="pi pi-exclamation-triangle text-2xl"></i>
      <p class="mt-2">{{ error }}</p>
    </div>
    
    <div v-else-if="qrData" class="text-center">
      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">QR Code for {{ qrData.item.title }}</h3>
        <p class="text-sm text-gray-600">{{ qrData.data.bookTitle }} - {{ qrData.data.chapterTitle }}</p>
      </div>
      
      <div class="qr-code-image mb-4">
        <img :src="qrData.qrCode" :alt="`QR Code for ${qrData.item.title}`" class="mx-auto border rounded-lg shadow-md" />
      </div>
      
      <div v-if="authStore.canEdit" class="mb-4">
        <label class="block text-sm font-medium mb-2">QR Code Style:</label>
        <Select
          v-model="selectedStyle"
          :options="styleOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select Style"
          class="w-full mb-3"
          @change="onStyleChange"
        />
      </div>

      <!-- Main Actions -->
      <div class="flex gap-2 justify-center mb-3 flex-wrap">
        <Button
          label="Preview"
          icon="pi pi-eye"
          @click="previewContent"
          severity="success"
          size="small"
        />
        <Button
          label="Download"
          icon="pi pi-download"
          @click="downloadQRCode"
          size="small"
        />
        <Button
          v-if="authStore.canEdit"
          label="Regenerate"
          icon="pi pi-refresh"
          @click="regenerateQRCode"
          severity="secondary"
          size="small"
        />
      </div>

      <!-- Edit/Delete Actions -->
      <div v-if="authStore.canEdit || (onDelete && authStore.canDelete)" class="flex gap-2 justify-center mb-4 flex-wrap">
        <Button
          v-if="authStore.canEdit"
          label="Edit"
          icon="pi pi-pencil"
          @click="editItem"
          severity="warning"
          size="small"
          outlined
        />
        <Button
          v-if="onDelete && authStore.canDelete"
          label="Delete"
          icon="pi pi-trash"
          @click="onDelete"
          severity="danger"
          size="small"
          outlined
        />
      </div>
      
      <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <p><strong>Type:</strong> {{ qrData.item.type }}</p>
        <p><strong>ID:</strong> {{ qrData.data.itemId }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import Button from 'primevue/button';
import Select from 'primevue/select';
import { useAuthStore } from '../../stores/auth';

interface Props {
  bookId: string;
  chapterId: string;
  itemId: string;
  onDelete?: () => void;
  onEdit?: () => void;
}

interface QRData {
  qrCode: string;
  data: {
    bookId: string;
    chapterId: string;
    itemId: string;
    bookTitle: string;
    chapterTitle: string;
    itemTitle: string;
    itemType: string;
    url: string;
  };
  item: {
    id: string;
    title: string;
    type: string;
    [key: string]: any;
  };
  styled?: boolean;
  displayUrl?: string;
}

const props = defineProps<Props>();
const authStore = useAuthStore();
const loading = ref(false);
const error = ref<string | null>(null);
const qrData = ref<QRData | null>(null);
const styled = ref(false);
const selectedStyle = ref('default');

const styleOptions = [
  { label: 'Default (Gray)', value: 'default' },
  { label: 'Blue Theme', value: 'blue' },
  { label: 'Green Theme', value: 'green' },
  { label: 'Purple Theme', value: 'purple' },
  { label: 'High Contrast', value: 'high_contrast' }
];

const generateQRCode = async (useStyled = false, style = 'default') => {
  loading.value = true;
  error.value = null;

  try {
    let url;
    if (useStyled) {
      url = `http://localhost:3001/api/qr-styled/${props.bookId}/${props.chapterId}/${props.itemId}?style=${style}`;
    } else {
      url = `http://localhost:3001/api/qr/${props.bookId}/${props.chapterId}/${props.itemId}`;
    }

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to generate QR code: ${response.status}`);
    }

    const data = await response.json();
    qrData.value = data;
    styled.value = useStyled || data.styled || false;
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to generate QR code';
    console.error('Error generating QR code:', err);
  } finally {
    loading.value = false;
  }
};

const onStyleChange = () => {
  generateQRCode(true, selectedStyle.value);
};

const regenerateQRCode = () => {
  generateQRCode(styled.value, selectedStyle.value);
};

const previewContent = () => {
  if (qrData.value?.displayUrl) {
    window.open(qrData.value.displayUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
  }
};

const downloadQRCode = () => {
  if (!qrData.value) return;

  const link = document.createElement('a');
  link.href = qrData.value.qrCode;
  link.download = `qr-code-${qrData.value.data.itemTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const editItem = () => {
  if (props.onEdit) {
    props.onEdit();
  }
};

// Watch for prop changes and regenerate QR code
watch([() => props.bookId, () => props.chapterId, () => props.itemId], () => {
  if (props.bookId && props.chapterId && props.itemId) {
    generateQRCode(true, selectedStyle.value); // Start with styled version
  }
}, { immediate: true });

onMounted(() => {
  if (props.bookId && props.chapterId && props.itemId) {
    generateQRCode(true, selectedStyle.value); // Start with styled version
  }
});
</script>

<style scoped>
.qr-code-container {
  max-width: 350px;
  max-height: 75vh;
  padding: 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.qr-code-image {
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.qr-code-image img {
  max-width: 280px;
  max-height: 280px;
  width: auto;
  height: auto;
  border-radius: 4px;
  display: block;
  margin: 0 auto;
}

/* Button container */
.qr-code-container .flex {
  flex-shrink: 0;
  margin: 0.5rem 0;
}

/* Info section */
.qr-code-container .text-xs {
  flex-shrink: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  margin-top: 0.5rem;
}

/* Button styling improvements */
.qr-code-container :deep(.p-button) {
  transition: all 0.2s ease;
  font-size: 0.8rem;
  border-radius: 6px;
}

.qr-code-container :deep(.p-button.p-button-sm) {
  padding: 0.4rem 0.75rem;
  font-size: 0.75rem;
}

.qr-code-container :deep(.p-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
}

.qr-code-container :deep(.p-button.p-button-outlined) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .qr-code-container {
    max-width: 100%;
    margin: 0;
    border-radius: 8px;
    padding: 0.5rem;
    max-height: 70vh;
  }

  .qr-code-image img {
    max-width: 240px;
    max-height: 240px;
  }

  .qr-code-container :deep(.p-button) {
    font-size: 0.7rem;
  }

  .qr-code-container :deep(.p-button.p-button-sm) {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .flex {
    gap: 0.375rem !important;
  }
}
</style>
