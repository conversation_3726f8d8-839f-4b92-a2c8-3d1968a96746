import express from 'express';
import cors from 'cors';
import { promises as fs } from 'fs';
import path from 'path';
// Import QRCodeStyling for Node.js environment
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { QRCodeStyling } = require('qr-code-styling/lib/qr-code-styling.common.js');
const nodeCanvas = require('canvas');
const { JSDOM } = require('jsdom');
import { fileURLToPath } from 'url';
import crypto from 'crypto';
import os from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Helper function to read books
async function readBooks() {
  try {
    const data = await fs.readFile(BOOKS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading books:', error);
    return [];
  }
}

// Helper function to write books
async function writeBooks(books) {
  try {
    await fs.writeFile(BOOKS_FILE, JSON.stringify(books, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing books:', error);
    return false;
  }
}

// Helper function to generate meaningful IDs
function generateMeaningfulId(type, books, bookId = null, chapterId = null) {
  switch (type) {
    case 'book':
      const bookCount = books.length + 1;
      return `book${bookCount}`;

    case 'chapter':
      const book = books.find(b => b.id === bookId);
      if (!book) return `ch${Date.now()}`;
      const chapterCount = book.chapters.length + 1;
      return `${bookId}-ch${chapterCount}`;

    case 'item':
      const targetBook = books.find(b => b.id === bookId);
      if (!targetBook) return `item${Date.now()}`;
      const targetChapter = targetBook.chapters.find(c => c.id === chapterId);
      if (!targetChapter) return `item${Date.now()}`;
      const itemCount = targetChapter.items.length + 1;
      return `${chapterId}-item${itemCount}`;

    default:
      return Date.now().toString();
  }
}

// Generate secure token for QR access
function generateSecureToken() {
  return crypto.randomBytes(32).toString('hex');
}

// Get the network IP address
function getNetworkIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost'; // fallback
}

// Store for QR tokens (in production, use Redis or database)
const qrTokens = new Map();

// Store for user sessions (in production, use Redis or database)
const userSessions = new Map();

// Default users (in production, use proper database with hashed passwords)
const defaultUsers = [
  {
    id: 'admin-1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In production, this should be hashed
    role: 'admin',
    createdAt: Date.now()
  },
  {
    id: 'editor-1',
    username: 'editor',
    email: '<EMAIL>',
    password: 'editor123', // In production, this should be hashed
    role: 'editor',
    createdAt: Date.now()
  },
  {
    id: 'viewer-1',
    username: 'viewer',
    email: '<EMAIL>',
    password: 'viewer123', // In production, this should be hashed
    role: 'viewer',
    createdAt: Date.now()
  }
];

// Simple authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  const session = userSessions.get(token);
  if (!session) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }

  req.user = session.user;
  next();
}

// Permission check middleware
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = getRolePermissions(req.user.role);
    if (!userPermissions[permission]) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

// Get role permissions
function getRolePermissions(role) {
  switch (role) {
    case 'viewer':
      return {
        canView: true,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'editor':
      return {
        canView: true,
        canEdit: true,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: true,
        canCreateBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    default:
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
  }
}

// Helper function to create smooth QR code with rounded modules
async function createSmoothQRCode(data, itemType, color = '#1f2937', backgroundColor = '#ffffff') {
  try {
    console.log(`Generating QR code for ${itemType} with color ${color} and background ${backgroundColor}`);
    const colors = getColorsFromHex(color, backgroundColor);

    const qrCodeOptions = {
      width: 800,  // Increased from 280 to 800 for better print quality
      height: 800, // Increased from 280 to 800 for better print quality
      data: data,
      dotsOptions: {
        color: colors.dark,
        type: "rounded"
      },
      backgroundOptions: {
        color: colors.light,
      },
      cornersSquareOptions: {
        color: colors.dark,
        type: "extra-rounded"
      },
      cornersDotOptions: {
        color: colors.dark,
        type: "dot"
      }
    };

    // Try to add icon if available
    const iconPath = path.join(__dirname, 'public', 'icons', `${itemType}.svg`);
    try {
      await fs.access(iconPath);
      console.log(`Using icon: ${iconPath}`);
      qrCodeOptions.image = iconPath;
      qrCodeOptions.imageOptions = {
        crossOrigin: "anonymous",
        margin: 20,
        imageSize: 0.3,
        hideBackgroundDots: true
      };
    } catch (error) {
      console.log(`Icon not found for type: ${itemType}, using default`);
    }

    console.log('Creating QRCodeStyling instance...');
    // Use the correct Node.js syntax from the documentation
    const qrCode = new QRCodeStyling({
      jsdom: JSDOM, // this is required
      nodeCanvas, // this is required
      ...qrCodeOptions,
      imageOptions: {
        saveAsBlob: true,
        crossOrigin: "anonymous",
        margin: 15,
        imageSize: 0.25,
        hideBackgroundDots: true,
        ...qrCodeOptions.imageOptions
      }
    });

    console.log('Getting raw data as buffer...');
    // Use getRawData with proper type specification
    const buffer = await qrCode.getRawData("png");

    if (!buffer) {
      throw new Error('Failed to generate QR code buffer');
    }

    console.log('QR code generated successfully, buffer size:', buffer.length);
    return buffer;

  } catch (error) {
    console.error('Error in createSmoothQRCode:', error);
    console.error('Error details:', error.message);
    throw new Error(`QR code generation failed: ${error.message}`);
  }
}

// Get colors from hex color
function getColorsFromHex(hexColor, backgroundColor) {
  // Validate hex color format
  if (!hexColor || !hexColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    hexColor = '#1f2937'; // Default gray
  }

  // Validate background color format
  if (!backgroundColor || !backgroundColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    backgroundColor = '#ffffff'; // Default white
  }

  return {
    dark: hexColor,
    light: backgroundColor
  };
}

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Find user (in production, use proper database lookup with hashed passwords)
    const user = defaultUsers.find(u => u.username === username && u.password === password);

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate session token
    const token = crypto.randomBytes(32).toString('hex');
    const session = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
        lastLogin: Date.now()
      },
      createdAt: Date.now()
    };

    // Store session (expires in 24 hours)
    userSessions.set(token, session);
    setTimeout(() => userSessions.delete(token), 24 * 60 * 60 * 1000);

    res.json({
      user: session.user,
      token: token,
      permissions: getRolePermissions(user.role)
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/auth/logout', authenticateToken, async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      userSessions.delete(token);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: req.user,
      permissions: getRolePermissions(req.user.role)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all books (requires authentication)
app.get('/api/books', authenticateToken, requirePermission('canView'), async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book (admin only)
app.post('/api/books', authenticateToken, requirePermission('canCreateBooks'), async (req, res) => {
  try {
    const books = await readBooks();
    const newBook = {
      id: generateMeaningfulId('book', books),
      title: req.body.title,
      description: req.body.description || '',
      chapters: []
    };

    books.push(newBook);
    const success = await writeBooks(books);

    if (success) {
      res.json(newBook);
    } else {
      res.status(500).json({ error: 'Failed to save book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book (admin only)
app.post('/api/books/:bookId/chapters', authenticateToken, requirePermission('canCreateChapters'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const newChapter = {
      id: generateMeaningfulId('chapter', books, req.params.bookId),
      title: req.body.title,
      items: []
    };

    book.chapters.push(newChapter);
    const success = await writeBooks(books);

    if (success) {
      res.json(newChapter);
    } else {
      res.status(500).json({ error: 'Failed to save chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter (editor or admin)
app.post('/api/books/:bookId/chapters/:chapterId/items', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const newItem = {
      id: generateMeaningfulId('item', books, req.params.bookId, req.params.chapterId),
      ...req.body
    };

    chapter.items.push(newItem);
    const success = await writeBooks(books);

    if (success) {
      res.json(newItem);
    } else {
      res.status(500).json({ error: 'Failed to save item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a book
app.delete('/api/books/:bookId', async (req, res) => {
  try {
    const books = await readBooks();
    const bookIndex = books.findIndex(b => b.id === req.params.bookId);

    if (bookIndex === -1) {
      return res.status(404).json({ error: 'Book not found' });
    }

    books.splice(bookIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Book deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a chapter
app.delete('/api/books/:bookId/chapters/:chapterId', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapterIndex = book.chapters.findIndex(c => c.id === req.params.chapterId);
    if (chapterIndex === -1) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    book.chapters.splice(chapterIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Chapter deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update an item in a chapter (editor or admin)
app.put('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Update the item while preserving the ID
    const updatedItem = {
      ...chapter.items[itemIndex],
      ...req.body,
      id: req.params.itemId // Ensure ID never changes
    };

    chapter.items[itemIndex] = updatedItem;
    const success = await writeBooks(books);

    if (success) {
      res.json(updatedItem);
    } else {
      res.status(500).json({ error: 'Failed to update item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete an item (admin only)
app.delete('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canDelete'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    chapter.items.splice(itemIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Item deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with default gray color and white background
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, '#1f2937', '#ffffff');
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code with custom colors
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }







    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with selected colors
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, color, backgroundColor);
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      styled: true,
      color: color,
      backgroundColor: backgroundColor,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Permanent display page for QR code access using item IDs
app.get('/item/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📚 Book Not Found</h1>
            <p>The requested book could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Chapter Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📖 Chapter Not Found</h1>
            <p>The requested chapter could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📄 Item Not Found</h1>
            <p>The requested item could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, book.title, chapter.title);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Legacy display page for backward compatibility (will show deprecation notice)
app.get('/display/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const tokenData = qrTokens.get(token);

    if (!tokenData) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invalid or Expired Link</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🔒 Access Denied</h1>
            <p>This link is invalid or has expired. Please generate a new QR code.</p>
            <p><small>Note: QR codes now use permanent links that never expire.</small></p>
          </div>
        </body>
        </html>
      `);
    }

    const { item, bookTitle, chapterTitle } = tokenData;

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, bookTitle, chapterTitle);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate beautiful display page
function generateDisplayPage(item, bookTitle, chapterTitle) {
  const getItemIcon = (type) => {
    const icons = {
      question: '❓',
      text: '📄',
      image: '🖼️',
      link: '🔗',
      map: '🗺️',
      diagram: '📊',
      'timed-question': '⏰'
    };
    return icons[type] || '📋';
  };

  const renderItemContent = (item) => {
    switch (item.type) {
      case 'question':
        return `
          <div class="question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            ${item.options ? `
              <div class="options-container" id="optionsContainer">
                ${item.options.map((option, index) => `
                  <div class="option" data-index="${index}" onclick="selectOption(${index}, ${item.correctAnswer})">
                    <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                    <span class="option-text">${option.text}</span>
                  </div>
                `).join('')}
              </div>
              <div id="result" class="result-container" style="display: none;">
                <div id="resultMessage" class="result-message"></div>
                <button onclick="resetQuestion()" class="reset-button">Try Again</button>
              </div>
            ` : ''}
          </div>
        `;
      case 'text':
        return `
          <div class="text-container">
            <div class="text-content">${item.content || 'No content available'}</div>
          </div>
        `;
      case 'image':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🖼️</span>
              <p>Image: ${item.url || 'No image URL provided'}</p>
            </div>
          </div>
        `;
      case 'map':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🗺️</span>
              <p>Map: ${item.title}</p>
              <small>Map image would be displayed here</small>
            </div>
          </div>
        `;
      case 'diagram':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">📊</span>
              <p>Diagram: ${item.title}</p>
              <small>Diagram image would be displayed here</small>
            </div>
          </div>
        `;
      case 'timed-question':
        return `
          <div class="timed-question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            <div class="timer-container">
              <div class="timer-display" id="timerDisplay">
                <span class="timer-icon">⏰</span>
                <span class="timer-text" id="timerText">${item.revealTimeSeconds || 30}</span>
                <span class="timer-label">seconds</span>
              </div>
            </div>
            <div class="answer-container" id="answerContainer" style="display: none;">
              <h3 class="answer-title">Answer:</h3>
              <div class="answer-content">${item.timedAnswer || 'No answer provided'}</div>
            </div>
            <button id="startTimerBtn" class="start-timer-button" onclick="startTimer()">Start Timer</button>
            <button id="resetTimerBtn" class="reset-timer-button" onclick="resetTimer()" style="display: none;">Reset</button>
          </div>
        `;
      default:
        return `<div class="default-content">Content not available</div>`;
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${item.title} - ${bookTitle}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          padding: 20px;
          line-height: 1.6;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }

        .item-icon {
          font-size: 3rem;
          margin-bottom: 15px;
          display: block;
        }

        .item-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 10px;
        }

        .breadcrumb {
          opacity: 0.9;
          font-size: 0.9rem;
        }

        .content {
          padding: 40px;
        }

        .question-container {
          text-align: center;
        }

        .question-title {
          font-size: 1.5rem;
          color: #1a202c;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .options-container {
          display: grid;
          gap: 15px;
          max-width: 600px;
          margin: 0 auto;
        }

        .option {
          display: flex;
          align-items: center;
          padding: 20px;
          background: #f7fafc;
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .option.correct {
          background: #f0fff4;
          border-color: #68d391;
          box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
        }

        .option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
          box-shadow: 0 4px 12px rgba(252, 129, 129, 0.2);
        }

        .option.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .option-letter {
          background: #4f46e5;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .option.correct .option-letter {
          background: #38a169;
        }

        .option.incorrect .option-letter {
          background: #e53e3e;
        }

        .option-text {
          flex: 1;
          font-size: 1.1rem;
        }

        .correct-indicator {
          color: #38a169;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .incorrect-indicator {
          color: #e53e3e;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .result-container {
          margin-top: 30px;
          text-align: center;
          padding: 20px;
          border-radius: 12px;
          animation: fadeIn 0.5s ease-out;
        }

        .result-message {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .result-message.correct {
          color: #38a169;
        }

        .result-message.incorrect {
          color: #e53e3e;
        }

        .reset-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .reset-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .text-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .text-content {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #2d3748;
          background: #f7fafc;
          padding: 30px;
          border-radius: 12px;
          border-left: 4px solid #4f46e5;
        }

        .image-container {
          text-align: center;
        }

        .image-placeholder {
          background: #f7fafc;
          border: 2px dashed #cbd5e0;
          border-radius: 12px;
          padding: 60px 30px;
          color: #718096;
        }

        .image-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 20px;
        }

        .timed-question-container {
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
        }

        .timer-container {
          margin: 30px 0;
        }

        .timer-display {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          border-radius: 20px;
          display: inline-flex;
          align-items: center;
          gap: 15px;
          font-size: 2rem;
          font-weight: bold;
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        .timer-icon {
          font-size: 2.5rem;
        }

        .timer-text {
          font-size: 3rem;
          min-width: 80px;
        }

        .timer-label {
          font-size: 1.2rem;
          opacity: 0.9;
        }

        .answer-container {
          background: #f0fff4;
          border: 2px solid #68d391;
          border-radius: 12px;
          padding: 30px;
          margin: 30px 0;
          animation: slideDown 0.5s ease-out;
        }

        .answer-title {
          color: #38a169;
          font-size: 1.5rem;
          margin-bottom: 15px;
          font-weight: 600;
        }

        .answer-content {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #2d3748;
          background: white;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #38a169;
        }

        @keyframes slideDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .start-timer-button, .reset-timer-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 10px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 10px;
        }

        .start-timer-button:hover, .reset-timer-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .reset-timer-button {
          background: #6b7280;
        }

        .reset-timer-button:hover {
          background: #4b5563;
        }

        .footer {
          background: #f7fafc;
          padding: 20px 40px;
          text-align: center;
          color: #718096;
          font-size: 0.9rem;
          border-top: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
          body { padding: 10px; }
          .container { border-radius: 15px; }
          .header { padding: 20px; }
          .item-title { font-size: 1.5rem; }
          .content { padding: 20px; }
          .question-title { font-size: 1.3rem; }
          .option { padding: 15px; }
          .option-text { font-size: 1rem; }
          .text-content { font-size: 1.1rem; padding: 20px; }
        }
      </style>
      <script>
        function selectOption(selectedIndex, correctAnswer) {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');
          const resultMessage = document.getElementById('resultMessage');

          // Disable all options
          options.forEach(option => {
            option.classList.add('disabled');
            option.onclick = null;
          });

          // Mark selected option
          options[selectedIndex].classList.add('selected');

          // Show correct/incorrect styling
          if (selectedIndex === correctAnswer) {
            options[selectedIndex].classList.add('correct');
            options[selectedIndex].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '🎉 Correct! Well done!';
            resultMessage.className = 'result-message correct';
          } else {
            options[selectedIndex].classList.add('incorrect');
            options[selectedIndex].innerHTML += '<span class="incorrect-indicator">✗</span>';
            options[correctAnswer].classList.add('correct');
            options[correctAnswer].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '❌ Incorrect. The correct answer is highlighted.';
            resultMessage.className = 'result-message incorrect';
          }

          // Show result
          resultContainer.style.display = 'block';
        }

        function resetQuestion() {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');

          // Reset all options
          options.forEach((option, index) => {
            option.className = 'option';
            option.onclick = () => selectOption(index, ${item.correctAnswer || 0});
            // Remove indicators
            const indicators = option.querySelectorAll('.correct-indicator, .incorrect-indicator');
            indicators.forEach(indicator => indicator.remove());
          });

          // Hide result
          resultContainer.style.display = 'none';
        }

        // Timed question functionality
        let timerInterval;
        let timeRemaining = ${item.revealTimeSeconds || 30};

        function startTimer() {
          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          startBtn.style.display = 'none';
          resetBtn.style.display = 'inline-block';

          timerInterval = setInterval(() => {
            timeRemaining--;
            timerText.textContent = timeRemaining;

            if (timeRemaining <= 0) {
              clearInterval(timerInterval);
              answerContainer.style.display = 'block';
              timerText.textContent = '0';
              resetBtn.textContent = 'Try Again';
            }
          }, 1000);
        }

        function resetTimer() {
          clearInterval(timerInterval);
          timeRemaining = ${item.revealTimeSeconds || 30};

          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          timerText.textContent = timeRemaining;
          answerContainer.style.display = 'none';
          startBtn.style.display = 'inline-block';
          resetBtn.style.display = 'none';
          resetBtn.textContent = 'Reset';
        }
      </script>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <span class="item-icon">${getItemIcon(item.type)}</span>
          <h1 class="item-title">${item.title}</h1>
          <div class="breadcrumb">${bookTitle} → ${chapterTitle}</div>
        </div>

        <div class="content">
          ${renderItemContent(item)}
        </div>

        <div class="footer">
          <p>📚 Accessed via QR Code • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

console.log('Starting server...');

app.listen(PORT, '0.0.0.0', () => {
  const networkIP = getNetworkIP();
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://${networkIP}:${PORT}`);
  console.log(`📚 API available at: http://${networkIP}:${PORT}/api/books`);
  console.log(`📱 QR codes will use: http://${networkIP}:${PORT}`);
});
