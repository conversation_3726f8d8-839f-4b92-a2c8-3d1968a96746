import type { TreeNode } from 'primevue/treenode';
import type { Book } from '../types/book';

const getItemIcon = (itemType: string): string => {
  const iconMap: Record<string, string> = {
    'question': 'pi pi-fw pi-question-circle',
    'text': 'pi pi-fw pi-info-circle',
    'image': 'pi pi-fw pi-image',
    'link': 'pi pi-fw pi-link',
    'map': 'pi pi-fw pi-map',
    'diagram': 'pi pi-fw pi-sitemap',
    'timed-question': 'pi pi-fw pi-clock'
  };
  return iconMap[itemType] || 'pi pi-fw pi-file';
};

export const NodeService = {
  getTreeNodes(book: Book): Promise<TreeNode[]> {
    return Promise.resolve(
      book.chapters.map(chapter => ({
        key: chapter.id,
        label: chapter.title,
        data: `Chapter: ${chapter.title}`,
        icon: 'pi pi-fw pi-book',
        selectable: true,
        children: chapter.items.map(item => ({
          key: item.id,
          label: item.title,
          data: `Item: ${item.type}`,
          icon: getItemIcon(item.type),
          selectable: true,
        })),
      }))
    );
  },
};