import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';

interface State {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
}

export const useMainStore = defineStore('main', {
  state: (): State => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
  }),

  actions: {
    async loadBooks() {
      this.loading = true;
      this.error = null;
      try {
        // Try API first, fallback to static file
        let response;
        try {
          const authStore = useAuthStore();
          response = await fetch('http://localhost:3001/api/books', {
            headers: {
              ...authStore.getAuthHeaders(),
            },
          });
        } catch (apiError) {
          console.log('API not available, falling back to static file');
          response = await fetch('/books.json');
        }

        if (!response.ok) {
          throw new Error(`Failed to load books: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Raw books data:', data);
        let booksArray = Array.isArray(data) ? data : Array.isArray(data.books) ? data.books : [];
        if (!Array.isArray(booksArray)) {
          throw new Error('Invalid books data: Expected an array or { books: [...] }');
        }
        this.books = booksArray;
      } catch (error) {
        this.error = String(error);
        this.books = [];
        console.error('Error loading books:', error);
      } finally {
        this.loading = false;
      }
    },

    async addBook(book: { title: string; description: string }) {
      try {
        const authStore = useAuthStore();
        const response = await fetch('http://localhost:3001/api/books', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders(),
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        this.books.push(newBook);
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      }
    },

    selectBook(id: string | null) {
      this.selectedBookId = id;
    },

    async addChapter(bookId: string, chapter: { title: string }) {
      try {
        const response = await fetch(`http://localhost:3001/api/books/${bookId}/chapters`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          book.chapters.push(newChapter);
        }
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapterCount = book ? book.chapters.length + 1 : 1;
        const fallbackChapter = {
          id: `${bookId}-ch${chapterCount}`,
          title: chapter.title,
          items: []
        };
        if (book) {
          book.chapters.push(fallbackChapter);
        }
        return fallbackChapter;
      }
    },

    async addItem(bookId: string, chapterId: string, item: Omit<Item, 'id'>) {
      try {
        const response = await fetch(`http://localhost:3001/api/books/${bookId}/chapters/${chapterId}/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.items.push(newItem);
        }
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        const itemCount = chapter ? chapter.items.length + 1 : 1;
        const fallbackItem = {
          id: `${chapterId}-item${itemCount}`,
          ...item
        };
        if (chapter) {
          chapter.items.push(fallbackItem);
        }
        return fallbackItem;
      }
    },

    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      try {
        const authStore = useAuthStore();
        const response = await fetch(`http://localhost:3001/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders(),
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = updatedItem;
          }
        }
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        return chapter?.items.find(i => i.id === itemId);
      }
    },

    getBook(id: string | undefined): Book | undefined {
      return this.books.find(book => book.id === id);
    },

    async deleteBook(bookId: string) {
      try {
        const response = await fetch(`http://localhost:3001/api/books/${bookId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        // Remove from local state
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }

        // Clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }

        return true;
      } catch (error) {
        console.error('Error deleting book:', error);
        // Fallback to local state only
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
        return true;
      }
    },

    async deleteChapter(bookId: string, chapterId: string) {
      try {
        const response = await fetch(`http://localhost:3001/api/books/${bookId}/chapters/${chapterId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }
        return true;
      }
    },

    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      try {
        const authStore = useAuthStore();
        const response = await fetch(`http://localhost:3001/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`, {
          method: 'DELETE',
          headers: {
            ...authStore.getAuthHeaders(),
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }
        return true;
      }
    },
  },
});